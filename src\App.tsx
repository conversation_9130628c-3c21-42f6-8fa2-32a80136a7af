import { ThemeProvider } from "@/components/theme-provider"
import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import MainPage from "@/pages/MainPage"
import Dashboard from "@/pages/app/Dashboard"
import AddClient from "@/pages/app/AddClient"
import ClientList from "@/pages/app/ClientList"
import ProtectedRoute from "@/components/ProtectedRoute"

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="planfuly-ui-theme">
      <Router>
        <Routes>
          <Route path="/" element={<MainPage />} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/clients"
            element={
              <ProtectedRoute>
                <ClientList />
              </ProtectedRoute>
            }
          />
          <Route
            path="/clients/new"
            element={
              <ProtectedRoute>
                <AddClient />
              </ProtectedRoute>
            }
          />
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
