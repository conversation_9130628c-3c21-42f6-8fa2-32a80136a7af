import { Layout } from "@/components/ui/layout"
import { But<PERSON> } from "@/components/ui/basic/button"
import { useNavigate } from "react-router-dom"

function Dashboard() {
  const navigate = useNavigate()

  const goToAdminMode = () => navigate("/clients")
  const goToPresentationMode = () => { }

  return (
    <Layout title="" className="bg-black">
      <div className="bg-white h-full">
        <Button size="lg" onClick={goToAdminMode}>Go to Admin Mode</Button>
        <Button size="lg" variant="outline" onClick={goToPresentationMode}>Go to Presentation Mode</Button>
      </div>
    </Layout>
  )
}

export default Dashboard
