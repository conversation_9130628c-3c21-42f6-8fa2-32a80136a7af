import { Layout } from "@/components/ui/layout"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/container/card"
import { Input } from "@/components/ui/form/input"
import { But<PERSON> } from "@/components/ui/basic/button"
import { useState } from "react"
import { useNavigate } from "react-router-dom"

function AddClient() {
  const navigate = useNavigate()
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: integrate with backend/Firebase
    navigate("/dashboard")
  }

  return (
    <Layout title="Add Client">
      <div className="max-w-xl mx-auto px-4 py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">New Client</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-1.5">
                <label className="text-sm font-medium">Name</label>
                <Input value={name} onChange={(e) => setName(e.target.value)} placeholder="Client name" required />
              </div>
              <div className="space-y-1.5">
                <label className="text-sm font-medium">Email</label>
                <Input type="email" value={email} onChange={(e) => setEmail(e.target.value)} placeholder="<EMAIL>" />
              </div>
              <div className="space-y-1.5">
                <label className="text-sm font-medium">Phone</label>
                <Input value={phone} onChange={(e) => setPhone(e.target.value)} placeholder="(*************" />
              </div>
              <div className="flex items-center gap-3 pt-2">
                <Button type="submit">Save</Button>
                <Button variant="outline" type="button" onClick={() => navigate(-1)}>Cancel</Button>
              </div>
            </form>
          </CardContent>
          <CardFooter />
        </Card>
      </div>
    </Layout>
  )
}

export default AddClient

